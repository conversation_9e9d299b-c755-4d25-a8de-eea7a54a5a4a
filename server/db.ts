import { PrismaClient } from '../generated/client'
import { applyTimezoneHandling } from './utils/database.util'
import { databaseMonitor } from './utils/database-monitor.util'
import { performanceMonitor } from './utils/performance-monitor.util'

// Enhanced Prisma configuration for better performance
export const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development'
    ? [
        { emit: 'event', level: 'query' },
        { emit: 'stdout', level: 'error' },
        { emit: 'stdout', level: 'warn' },
      ]
    : [
        { emit: 'stdout', level: 'error' },
      ],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // Note: Connection pooling is handled by the database connection string
  // and Prisma's internal connection management
})

// Enhanced query logging for performance monitoring
if (process.env.NODE_ENV === 'development') {
  prisma.$on('query', (e) => {
    const duration = e.duration;
    const query = e.query;

    // Record query metrics for monitoring
    databaseMonitor.recordQuery(duration, query);
    performanceMonitor.recordDatabaseQuery(query, duration);

    // Log slow queries (> 100ms)
    if (duration > 100) {
      console.warn(`🐌 Slow Query (${duration}ms):`, {
        query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
        duration: `${duration}ms`,
        params: e.params,
      });
    }
  });

  // Start periodic database health reporting
  databaseMonitor.startPeriodicReporting(prisma, 15); // Every 15 minutes
}

// Apply timezone handling
applyTimezoneHandling(prisma)

// Graceful shutdown handling
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});
