# King Collectibles - Deployment Guide

This guide covers the enhanced deployment setup for King Collectibles with <PERSON><PERSON>, including troubleshooting and best practices.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- `.env.production` file configured
- Access to the deployment server

### Automatic Deployment (GitHub Actions)
Deployments are automatically triggered when code is pushed to the `main` branch.

### Manual Deployment
```bash
# Run the deployment script
./scripts/deploy.sh

# Or specific commands
./scripts/deploy.sh deploy   # Full deployment
./scripts/deploy.sh health   # Health check only
./scripts/deploy.sh logs     # View logs
./scripts/deploy.sh backup   # Create backup
./scripts/deploy.sh cleanup  # Clean old images
./scripts/deploy.sh rollback # Rollback deployment
```

## 📁 File Structure

```
├── Dockerfile                 # Multi-stage Docker build
├── docker-compose.yml         # Production compose configuration
├── .github/workflows/deploy.yml # CI/CD pipeline
├── scripts/deploy.sh          # Manual deployment script
├── mysql-init/01-init.sql     # Database initialization
├── src/app/api/health/        # Health check endpoint
└── DEPLOYMENT.md              # This file
```

## 🔧 Configuration Files

### Environment Variables (.env.production)
```env
# Database
DATABASE_URL="mysql://user:password@db:3306/king_collectible?timezone=Asia/Jakarta"
DB_ROOT_PASSWORD="your-root-password"
DB_NAME="king_collectible"
DB_USER="your-db-user"
DB_PASSWORD="your-db-password"

# NextAuth
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-nextauth-secret"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# API
NEXT_PUBLIC_API_URL="https://your-domain.com/api/v1"
JWT_SECRET="your-jwt-secret"

# Payment Gateway
XENDIT_SECRET_KEY="your-xendit-secret"
XENDIT_WEBHOOK_TOKEN="your-xendit-webhook-token"

# Timezone
TZ="Asia/Jakarta"
DEFAULT_TIMEZONE="Asia/Jakarta"
```

## 🐳 Docker Configuration

### Dockerfile Features
- **Multi-stage build**: Optimized for production
- **Standalone output**: Self-contained Next.js application
- **Runtime migrations**: Database migrations run at startup
- **Health checks**: Built-in health monitoring
- **Security**: Non-root user, minimal attack surface

### Docker Compose Features
- **Health checks**: Both app and database monitoring
- **Dependency management**: App waits for database
- **Logging**: Structured logging with rotation
- **Networks**: Isolated network for services
- **Persistence**: MySQL data persistence

## 🔍 Health Monitoring

### Health Check Endpoint
- **URL**: `/api/health`
- **Method**: GET or HEAD
- **Response**: JSON with system status

### Example Response
```json
{
  "status": "healthy",
  "timestamp": "2025-01-24T10:30:00.000Z",
  "uptime": 3600,
  "environment": "production",
  "version": "1.0.0",
  "database": "connected",
  "memory": {
    "used": 128,
    "total": 256
  }
}
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check database status
docker compose ps db

# View database logs
docker compose logs db

# Test database connection
docker compose exec db mysql -u root -p
```

#### 2. Prisma Migration Errors
```bash
# Reset database (⚠️ DATA LOSS)
docker compose exec app npx prisma migrate reset

# Deploy migrations manually
docker compose exec app npx prisma migrate deploy

# Generate Prisma client
docker compose exec app npx prisma generate
```

#### 3. Application Won't Start
```bash
# View application logs
docker compose logs app

# Check health endpoint
curl http://localhost:3000/api/health

# Restart application
docker compose restart app
```

#### 4. Build Failures
```bash
# Clear Docker cache
docker builder prune -af

# Rebuild without cache
docker compose build --no-cache

# Check Dockerfile syntax
docker build --dry-run .
```

### Log Analysis
```bash
# View all logs
docker compose logs

# Follow logs in real-time
docker compose logs -f

# View specific service logs
docker compose logs app
docker compose logs db

# View last 100 lines
docker compose logs --tail=100
```

## 🔄 Backup and Recovery

### Automatic Backups
The deployment script automatically creates database backups before deployment.

### Manual Backup
```bash
# Create backup
./scripts/deploy.sh backup

# Or manually
docker compose exec db mysqldump -u root -p king_collectible > backup.sql
```

### Recovery
```bash
# Restore from backup
docker compose exec -T db mysql -u root -p king_collectible < backup.sql

# Or use rollback script
./scripts/deploy.sh rollback
```

## 📊 Performance Optimization

### Database Optimization
- Connection pooling configured
- Optimized MySQL settings
- Proper indexes via Prisma

### Application Optimization
- Standalone Next.js build
- Image optimization enabled
- Package import optimization

### Docker Optimization
- Multi-stage builds
- Layer caching
- Minimal base images

## 🔒 Security

### Security Headers
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: origin-when-cross-origin

### Container Security
- Non-root user
- Minimal attack surface
- Regular security updates

## 📈 Monitoring

### Health Checks
- Application: `/api/health`
- Database: MySQL ping
- Docker: Container health status

### Metrics
- Memory usage
- Uptime tracking
- Database connectivity
- Response times

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow
1. **Test**: Linting and type checking
2. **Build**: Docker image creation
3. **Deploy**: Automated deployment to VPS
4. **Verify**: Health checks and validation

### Manual Triggers
- Force deployment option
- Skip tests option
- Rollback capability

## 📞 Support

For deployment issues:
1. Check the logs: `./scripts/deploy.sh logs`
2. Run health check: `./scripts/deploy.sh health`
3. Create backup: `./scripts/deploy.sh backup`
4. Contact the development team

## 🔄 Updates

To update the deployment configuration:
1. Modify the relevant files
2. Test locally with Docker Compose
3. Commit and push to trigger CI/CD
4. Monitor deployment logs
5. Verify health checks pass
