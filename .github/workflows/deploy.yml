name: CI/CD - Build & Deploy on PR Merge to Main

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npm run type-check

      # - name: Run tests
      #   run: npm test

  build:
    name: Build & Push Docker Image
    runs-on: ubuntu-latest
    needs: test
    if: success() || github.event.inputs.force_deploy == 'true'

    env:
      IMAGE_NAME: ${{ secrets.DOCKERHUB_USERNAME }}/king-collectible

    outputs:
      image_tag: ${{ steps.meta.outputs.tags }}
      image_digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and Push Docker Image
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64
          # No build args needed - database operations happen at runtime

  deploy:
    name: Deploy to VPS
    runs-on: ubuntu-latest
    needs: build
    environment: Production

    env:
      SERVER_USER: ${{ secrets.SERVER_USER }}
      SERVER_IP: ${{ secrets.SERVER_IP }}
      IMAGE_NAME: ${{ secrets.DOCKERHUB_USERNAME }}/king-collectible

    steps:
      - name: Set up SSH access
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known_hosts
        run: ssh-keyscan -H ${{ env.SERVER_IP }} >> ~/.ssh/known_hosts

      - name: Deploy to VPS
        run: |
          ssh ${{ env.SERVER_USER }}@${{ env.SERVER_IP }} <<'EOF'
            set -e
            cd /var/www/king-collectible

            echo "🔍 Checking deployment directory..."
            pwd
            ls -la

            echo "[1/6] 🔌 Stopping old containers..."
            docker compose down --remove-orphans || true

            echo "[2/6] 🔄 Logging in to Docker Hub..."
            echo "${{ secrets.DOCKERHUB_PASSWORD }}" | docker login -u "${{ secrets.DOCKERHUB_USERNAME }}" --password-stdin

            echo "[3/6] ⬇️ Pulling latest image..."
            docker compose pull

            echo "[4/6] 🗄️ Checking database status..."
            docker compose up -d db
            sleep 10

            echo "[5/6] 🚀 Starting application..."
            docker compose up -d --remove-orphans

            echo "[6/6] ✅ Verifying deployment..."
            sleep 30
            if docker compose ps | grep -q "Up"; then
              echo "✅ Deployment successful!"
              docker compose ps
            else
              echo "❌ Deployment failed!"
              docker compose logs --tail=50
              exit 1
            fi

            echo "🧹 Cleaning up..."
            docker image prune -af --filter "until=24h"
            docker system prune -f --filter "until=24h"
          EOF

      - name: Health Check
        run: |
          echo "🏥 Performing health check..."
          for i in {1..10}; do
            if curl -f http://${{ env.SERVER_IP }}:3000/api/health; then
              echo "✅ Health check passed!"
              exit 0
            fi
            echo "⏳ Attempt $i failed, retrying in 10 seconds..."
            sleep 10
          done
          echo "❌ Health check failed after 10 attempts"
          exit 1
