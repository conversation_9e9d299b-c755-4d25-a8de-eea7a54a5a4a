#!/bin/bash

# King Collectibles Deployment Script
# This script handles the deployment process with proper error handling and rollback capabilities

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_IMAGE="reihanpraja/king-collectible:latest"
COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root or with sudo
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        warning "Running as root. Consider using a non-root user with docker group membership."
    fi
}

# Check if required files exist
check_requirements() {
    log "Checking deployment requirements..."
    
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        error "docker-compose.yml not found!"
        exit 1
    fi
    
    if [[ ! -f ".env.production" ]]; then
        error ".env.production file not found!"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running!"
        exit 1
    fi
    
    success "All requirements met"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup database if container is running
    if docker compose ps db | grep -q "Up"; then
        BACKUP_FILE="$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
        docker compose exec -T db mysqldump -u root -p"${DB_ROOT_PASSWORD}" king_collectible > "$BACKUP_FILE" 2>/dev/null || {
            warning "Database backup failed, but continuing deployment..."
        }
        success "Database backup created: $BACKUP_FILE"
    else
        warning "Database container not running, skipping backup"
    fi
}

# Deploy application
deploy() {
    log "Starting deployment process..."
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker compose down --remove-orphans || true
    
    # Pull latest images
    log "Pulling latest Docker images..."
    docker compose pull
    
    # Start database first
    log "Starting database..."
    docker compose up -d db
    
    # Wait for database to be ready
    log "Waiting for database to be ready..."
    for i in {1..30}; do
        if docker compose exec -T db mysqladmin ping -h localhost -u root -p"${DB_ROOT_PASSWORD}" >/dev/null 2>&1; then
            success "Database is ready"
            break
        fi
        if [[ $i -eq 30 ]]; then
            error "Database failed to start after 5 minutes"
            exit 1
        fi
        sleep 10
    done
    
    # Start application
    log "Starting application..."
    docker compose up -d
    
    # Wait for application to be ready
    log "Waiting for application to be ready..."
    for i in {1..20}; do
        if curl -f http://localhost:3000/api/health >/dev/null 2>&1; then
            success "Application is ready"
            break
        fi
        if [[ $i -eq 20 ]]; then
            error "Application failed to start after 10 minutes"
            show_logs
            exit 1
        fi
        sleep 30
    done
    
    success "Deployment completed successfully!"
}

# Show container logs
show_logs() {
    log "Showing recent logs..."
    docker compose logs --tail=50
}

# Health check
health_check() {
    log "Performing health check..."
    
    if curl -f http://localhost:3000/api/health; then
        success "Health check passed"
        return 0
    else
        error "Health check failed"
        return 1
    fi
}

# Rollback function
rollback() {
    error "Deployment failed. Starting rollback..."
    
    # Stop current containers
    docker compose down --remove-orphans || true
    
    # Restore from backup if available
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/db_backup_*.sql 2>/dev/null | head -n1)
    if [[ -n "$LATEST_BACKUP" ]]; then
        log "Restoring database from backup: $LATEST_BACKUP"
        docker compose up -d db
        sleep 30
        docker compose exec -T db mysql -u root -p"${DB_ROOT_PASSWORD}" king_collectible < "$LATEST_BACKUP"
    fi
    
    warning "Rollback completed. Please check the system manually."
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old Docker images and containers..."
    docker image prune -af --filter "until=24h" || true
    docker system prune -f --filter "until=24h" || true
    success "Cleanup completed"
}

# Main deployment process
main() {
    log "Starting King Collectibles deployment..."
    
    # Load environment variables
    if [[ -f ".env.production" ]]; then
        source .env.production
    fi
    
    check_permissions
    check_requirements
    create_backup
    
    # Deploy with error handling
    if deploy; then
        if health_check; then
            cleanup
            success "Deployment completed successfully!"
            docker compose ps
        else
            rollback
            exit 1
        fi
    else
        rollback
        exit 1
    fi
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "logs")
        show_logs
        ;;
    "health")
        health_check
        ;;
    "backup")
        create_backup
        ;;
    "cleanup")
        cleanup
        ;;
    "rollback")
        rollback
        ;;
    *)
        echo "Usage: $0 {deploy|logs|health|backup|cleanup|rollback}"
        echo "  deploy  - Full deployment process (default)"
        echo "  logs    - Show container logs"
        echo "  health  - Perform health check"
        echo "  backup  - Create database backup"
        echo "  cleanup - Clean up old Docker images"
        echo "  rollback - Rollback to previous state"
        exit 1
        ;;
esac
