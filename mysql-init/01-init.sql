-- MySQL initialization script for King Collectibles
-- This script ensures proper database setup with optimal settings

-- Set timezone
SET time_zone = '+07:00';

-- Ensure proper character set and collation
ALTER DATABASE king_collectible CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create indexes for better performance if they don't exist
-- Note: Prisma migrations will handle most of this, but these are fallbacks

-- Optimize MySQL settings for the application
SET GLOBAL innodb_buffer_pool_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 28800; -- 8 hours
SET GLOBAL interactive_timeout = 28800; -- 8 hours

-- Log successful initialization
SELECT 'Database initialization completed successfully' as status;
