'use client'
import React, { memo, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Grid,
  GridItem,
  Skeleton,
  Badge
} from '@chakra-ui/react';
import { useBestRelatedProducts } from '@/services/useRelatedProductsQuery';
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext';
import ProductCard from '@/components/product/ProductCard';
import { formatDistanceToNow } from 'date-fns';
import { convertRelatedProductsToProductItems } from '@/utils/productUtils';
import { ProductItem } from '@/types/product';

interface RelatedProductsProps {
  productId: string;
  categoryId: string;
  sellerId: string;
  currentProductName: string;
  limit?: number;
}

// Memoized skeleton component
const RelatedProductsSkeleton = memo(({ count = 6 }: { count?: number }) => (
  <Grid templateColumns={{ base: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' }} gap={6}>
    {Array.from({ length: count }).map((_, index) => (
      <GridItem key={index}>
        <VStack align="stretch" gap={3}>
          <Skeleton height="200px" borderRadius="lg" />
          <Skeleton height="20px" />
          <Skeleton height="16px" width="60%" />
          <HStack justify="space-between">
            <Skeleton height="16px" width="40%" />
            <Skeleton height="16px" width="30%" />
          </HStack>
        </VStack>
      </GridItem>
    ))}
  </Grid>
));

RelatedProductsSkeleton.displayName = 'RelatedProductsSkeleton';

// Memoized empty state component
const EmptyRelatedProducts = memo(() => (
  <Box textAlign="center" py={8}>
    <Text color="gray.600" fontSize="lg">
      No related products found
    </Text>
    <Text color="gray.500" fontSize="sm" mt={2}>
      Check back later for similar items
    </Text>
  </Box>
));

EmptyRelatedProducts.displayName = 'EmptyRelatedProducts';

// Memoized product item component
const RelatedProductItem = memo(({ product, currency, formatPrice, convertPrice }: {
  product: any;
  currency: string;
  formatPrice: (amount: number, currency: string) => string;
  convertPrice: (amount: number, fromCurrency: string) => number;
}) => {
  // Transform product data for ProductCard
  const transformedProduct = useMemo(() => ({
    id: product.id,
    slug: product.slug || '',
    title: product.itemName,
    image: product.images.find((img: any) => img.isMain)?.imageUrl || product.images[0]?.imageUrl || '',
    images: product.images.map((img: any) => img.imageUrl),
    price: formatPrice(convertPrice(product.priceUSD, 'USD'), currency),
    bids: product.bidCount?.toString() || '0',
    type: product.sellType as 'auction' | 'buy-now',
    timeLeft: product.auctionEndDate ?
      formatDistanceToNow(new Date(product.auctionEndDate), { addSuffix: true }) :
      undefined,
    category: product.category?.name,
    seller: `${product.seller?.firstName || ''} ${product.seller?.lastName || ''}`.trim(),
    currentBid: product.bids?.[0] ?
      formatPrice(convertPrice(Number(product.bids[0].amount), 'USD'), currency) :
      undefined
  }), [product, currency, formatPrice, convertPrice]);

  return (
    <ProductCard
      item={transformedProduct}
      containerProps={{
        transition: 'all 0.2s ease-in-out',
        _hover: {
          transform: 'translateY(-4px)',
          shadow: 'lg'
        }
      }}
    />
  );
});

RelatedProductItem.displayName = 'RelatedProductItem';



const RelatedProducts: React.FC<RelatedProductsProps> = memo(({
  productId,
  categoryId,
  sellerId,
  limit = 6
}) => {
  const { formatPrice, convertPrice, currency } = useCurrencyLanguage();

  const {
    products,
    isLoading,
    error,
    categoryData,
    sellerData
  } = useBestRelatedProducts(productId, categoryId, sellerId, limit);

  const relatedProduct: ProductItem[] = convertRelatedProductsToProductItems(products, formatPrice, convertPrice, currency);

  // Memoized section title
  const sectionTitle = useMemo(() => {
    if (categoryData?.categoryName) {
      return `More from ${categoryData.categoryName}`;
    }
    return 'Related Products';
  }, [categoryData?.categoryName]);

  // Memoized relationship badges
  const relationshipInfo = useMemo(() => {
    const info = [];
    if (categoryData?.relatedProducts && categoryData.relatedProducts.length > 0) {
      info.push({
        label: `${categoryData.relatedProducts.length} from same category`,
        color: 'blue'
      });
    }
    if (sellerData?.relatedProducts && sellerData.relatedProducts.length > 0) {
      info.push({
        label: `${sellerData.relatedProducts.length} from same seller`,
        color: 'green'
      });
    }
    return info;
  }, [categoryData, sellerData]);

  if (isLoading) {
    return (
      <Box>
        <VStack align="stretch" gap={6}>
          <VStack align="start" gap={2}>
            <Skeleton height="32px" width="250px" />
            <Skeleton height="16px" width="400px" />
          </VStack>
          <RelatedProductsSkeleton count={limit} />
        </VStack>
      </Box>
    );
  }

  if (error) {
    console.error('Related products error:', error);
    return null; // Fail silently for better UX
  }

  if (!products || products.length === 0) {
    return <EmptyRelatedProducts />;
  }

  return (
    <Box>
      <VStack align="stretch" gap={6}>
        {/* Header */}
        <VStack align="start" gap={3}>
          <Heading size="lg" color="gray.800">
            {sectionTitle}
          </Heading>

          <Text color="gray.600" fontSize="md">
            Discover similar items you might like
          </Text>

          {/* Relationship badges */}
          {relationshipInfo.length > 0 && (
            <HStack gap={2} wrap="wrap">
              {relationshipInfo.map((info, index) => (
                <Badge key={index} colorScheme={info.color} size="sm">
                  {info.label}
                </Badge>
              ))}
            </HStack>
          )}
        </VStack>

        {/* Products Grid */}
        <Grid
          templateColumns={{
            base: '1fr',
            sm: 'repeat(2, 1fr)',
            lg: 'repeat(3, 1fr)',
            xl: 'repeat(5, 1fr)'
          }}
        >
          {relatedProduct.map((product) => (
            <GridItem key={product.id}>
              <ProductCard
                imageContainerProps={{
                  h: { base: '200px', md: '300px' },
                  w: { base: '170px', md: '250px' },
                }}
                imageProps={{
                  maxW: { base: '140px', md: '220px' },
                  maxH: { base: '160px', md: '240px' },
                }}
                key={product.id}
                item={product}
              />
            </GridItem>
          ))}
        </Grid>

        {/* Show more hint */}
        {products.length >= limit && (
          <Box textAlign="center" pt={4}>
            <Text fontSize="sm" color="gray.500">
              Showing {products.length} related products
            </Text>
          </Box>
        )}
      </VStack>
    </Box>
  );
});

RelatedProducts.displayName = 'RelatedProducts';

export default RelatedProducts;
