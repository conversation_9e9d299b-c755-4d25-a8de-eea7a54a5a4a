"use client"
import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Text,
  Image,
  Badge,
  Stack,
  HStack,
  Button,
  Skeleton
} from '@chakra-ui/react'
import { useProductsQuery, Product, ProductQueryParams } from '@/services/useProductQuery'
import { formatDistanceToNowJakarta } from '@/utils/timezone'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { safeCurrencyConversion, safeNumberConversion } from '@/utils/productUtils'

interface ProductListProps {
  filters?: Partial<ProductQueryParams>;
  showPagination?: boolean;
  limit?: number;
}

const ProductCard: React.FC<{ product: Product }> = ({ product }) => {
  const { formatPrice, convertPrice, currency } = useCurrencyLanguage()
  const mainImage = product.images.find(img => img.isMain) || product.images[0];
  const timeLeft = product.auctionEndDate
    ? formatDistanceToNowJakarta(product.auctionEndDate)
    : null;

  // Safe conversion of price from USD to current currency
  const priceAmount = safeNumberConversion(product.priceUSD);
  const displayPrice = safeCurrencyConversion(priceAmount, convertPrice, 'USD', currency);
  const formattedPrice = formatPrice(displayPrice, currency);

  // Safe conversion of current bid
  const currentBidAmount = safeNumberConversion(product.currentBid);
  const currentBidPrice = currentBidAmount > 0
    ? safeCurrencyConversion(currentBidAmount, convertPrice, 'USD', currency)
    : displayPrice;
  const formattedCurrentBid = formatPrice(currentBidPrice, currency);

  // Check if auction has ended
  const isAuctionEnded = Boolean(product.sellType === 'auction' &&
                        product.auctionEndDate &&
                        new Date(product.auctionEndDate) <= new Date());

  console.log(`ProductList: ${product.itemName} - Original: ${product.priceUSD} USD, Converted: ${displayPrice} ${currency}, Formatted: ${formattedPrice}, Auction Ended: ${isAuctionEnded}`);

  return (
    <Box
      borderWidth="1px"
      borderRadius="lg"
      overflow="hidden"
      bg="white"
      shadow="sm"
      _hover={{ shadow: "md", transform: "translateY(-2px)" }}
      transition="all 0.2s"
      cursor="pointer"
    >
      {/* Product Image */}
      <Box position="relative" h="200px">
        {mainImage ? (
          <Image
            src={mainImage.imageUrl}
            alt={mainImage.altText || product.itemName}
            w="100%"
            h="100%"
            objectFit="cover"
          />
        ) : (
          <Box
            w="100%"
            h="100%"
            bg="gray.100"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Text color="gray.500">No Image</Text>
          </Box>
        )}

        {/* Sell Type Badge */}
        <Badge
          position="absolute"
          top={2}
          left={2}
          colorScheme={product.sellType === 'auction' ? 'blue' : 'green'}
          variant="solid"
        >
          {product.sellType === 'auction' ? 'Auction' : 'Buy Now'}
        </Badge>

        {/* Status Badge */}
        {product.status !== 'active' && (
          <Badge
            position="absolute"
            top={2}
            right={2}
            colorScheme="gray"
            variant="solid"
          >
            {product.status.toUpperCase()}
          </Badge>
        )}
      </Box>

      {/* Product Info */}
      <Stack p={4} gap={3}>
        <Text
          fontWeight="bold"
          fontSize="lg"
          lineClamp={2}
          minH="3rem"
        >
          {product.itemName}
        </Text>

        <HStack justify="space-between" align="center">
          <Stack gap={1}>
            <Text fontSize="sm" color="gray.600">
              {product.category.name} • {product.itemType.name}
            </Text>
            <Text fontSize="sm" color="gray.500">
              by {product.seller.firstName} {product.seller.lastName}
            </Text>
          </Stack>
        </HStack>

        {/* Price and Bid Info */}
        <Stack gap={2}>
          {product.sellType === 'auction' ? (
            <>
              <HStack justify="space-between">
                <Text fontSize="sm" color="gray.600">
                  {isAuctionEnded ? 'Final Bid:' : 'Current Bid:'}
                </Text>
                <Text
                  fontWeight="bold"
                  color={isAuctionEnded ? 'red.600' : 'gray.800'}
                >
                  {formattedCurrentBid}
                </Text>
              </HStack>
              {product.bidCount > 0 && (
                <Text fontSize="xs" color="gray.500">
                  {product.bidCount} bid{product.bidCount !== 1 ? 's' : ''}
                </Text>
              )}
              {isAuctionEnded ? (
                <Text fontSize="xs" color="red.600" fontWeight="bold">
                  Auction Ended
                </Text>
              ) : (
                timeLeft && product.status === 'active' && (
                  <Text fontSize="xs" color="red.500" fontWeight="medium">
                    Ends {timeLeft}
                  </Text>
                )
              )}
            </>
          ) : (
            <HStack justify="space-between">
              <Text fontSize="sm" color="gray.600">Price:</Text>
              <Text fontWeight="bold" color="green.600">
                {formattedPrice}
              </Text>
            </HStack>
          )}
        </Stack>

        {/* Action Button */}
        {product.sellType === 'auction' ? (
          <Button
            size="sm"
            colorScheme={isAuctionEnded ? "gray" : "blue"}
            variant="outline"
            w="full"
            disabled={product.status !== 'active' || isAuctionEnded}
          >
            {isAuctionEnded ? 'Auction Ended' : 'Place Bid'}
          </Button>
        ) : (
          <Button
            size="sm"
            colorScheme="green"
            variant="outline"
            w="full"
            disabled={product.status !== 'active'}
          >
            Add to Cart
          </Button>
        )}
      </Stack>
    </Box>
  );
};

const ProductListSkeleton: React.FC = () => (
  <Grid templateColumns="repeat(auto-fill, minmax(280px, 1fr))" gap={6}>
    {Array.from({ length: 8 }).map((_, index) => (
      <Box key={index} borderWidth="1px" borderRadius="lg" overflow="hidden" bg="white">
        <Skeleton height="200px" />
        <Stack p={4} gap={3}>
          <Skeleton height="20px" />
          <Skeleton height="16px" width="60%" />
          <HStack justify="space-between">
            <Skeleton height="14px" width="40%" />
            <Skeleton height="16px" width="30%" />
          </HStack>
          <Skeleton height="32px" />
        </Stack>
      </Box>
    ))}
  </Grid>
);

const ProductList: React.FC<ProductListProps> = ({
  filters = {},
  showPagination = true,
  limit = 12
}) => {
  const [currentPage, setCurrentPage] = useState(1);

  const queryParams: ProductQueryParams = {
    page: currentPage,
    limit,
    ...filters
  };

  const {
    data,
    isLoading,
    error,
    isError
  } = useProductsQuery(queryParams);

  if (isLoading) {
    return <ProductListSkeleton />;
  }

  if (isError) {
    return (
      <Box textAlign="center" py={10}>
        <Box bg="red.50" border="1px solid" borderColor="red.200" borderRadius="md" p={4}>
          <Text color="red.600" fontWeight="bold" mb={2}>
            Error loading products!
          </Text>
          <Text color="red.500" fontSize="sm">
            {error instanceof Error ? error.message : 'Failed to load products'}
          </Text>
        </Box>
      </Box>
    );
  }

  if (!data || data.products.length === 0) {
    return (
      <Box textAlign="center" py={10}>
        <Text fontSize="lg" color="gray.500">
          No products found
        </Text>
      </Box>
    );
  }

  return (
    <Stack gap={6}>
      {/* Products Grid */}
      <Grid templateColumns="repeat(auto-fill, minmax(280px, 1fr))" gap={6}>
        {data.products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </Grid>

      {/* Pagination */}
      {showPagination && data.pagination.totalPages > 1 && (
        <HStack justify="center" gap={2}>
          <Button
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={!data.pagination.hasPrev}
          >
            Previous
          </Button>

          <Text fontSize="sm" color="gray.600">
            Page {data.pagination.page} of {data.pagination.totalPages}
          </Text>

          <Button
            size="sm"
            onClick={() => setCurrentPage(prev => prev + 1)}
            disabled={!data.pagination.hasNext}
          >
            Next
          </Button>
        </HStack>
      )}
    </Stack>
  );
};

export default ProductList;
